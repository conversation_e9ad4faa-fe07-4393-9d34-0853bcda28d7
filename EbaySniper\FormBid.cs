using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraLayout.Utils;
using eBay.Service.Call;
using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;
using System;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using uBuyFirst.API.TradingAPI;
using uBuyFirst.Auth;
using uBuyFirst.Data;
using uBuyFirst.Images;
using uBuyFirst.Item;
using uBuyFirst.Network;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;
using uBuyFirst.Properties;
using uBuyFirst.Purchasing;
using uBuyFirst.Purchasing.BuyAPI;
using uBuyFirst.Stats;
using uBuyFirst.Tools;

namespace uBuyFirst
{
    public partial class FormBid : RibbonForm
    {
        private DataList D;
        private bool _isLayoutLoaded;
        private DataRow Row;
        private CancellationToken _getItemCancelToken;
        private CancellationTokenSource _getItemCancelTokenSourceTask;
        private OrderService _orderService;
        private Stopwatch _checkoutStopwatch;

        private Size PlaceOfferWindowSize { get; set; }

        private Point PlaceOfferWindowLocation { get; set; }
        public bool MainFormClosing { get; set; }

        public TradingAPIService TradingAPIService { get; }

        #region Init

        public FormBid()
        {
            InitializeComponent();
            InitializeLayout();
            ResetGetItemCancelToken();

            if (UserSettings.MakeOfferMessages.Count == 0)
            {
                UserSettings.MakeOfferMessages.Add("Message 1", "");
            }

            boxMakeOfferMessages.Properties.DataSource = UserSettings.MakeOfferMessages;
            boxMakeOfferMessages.Properties.DisplayMember = "Key";
            TradingAPIService = new TradingAPIService();
        }

        private void ResetGetItemCancelToken()
        {
            _getItemCancelTokenSourceTask = new CancellationTokenSource();
            _getItemCancelToken = _getItemCancelTokenSourceTask.Token;
        }

        public async void SetupFormInfo(DataRow row)
        {
            _checkoutStopwatch = new Stopwatch();
            _checkoutStopwatch.Start();
            try
            {
                SetFormSizeAndLocation();
                Row = row;
                D = (DataList)Row["Blob"];

                layoutControl1.BeginUpdate();
                pictureEdit1.Image = null;
                lcItemPrice.BackColor = Color.Empty;
                if (lciItemPrice.AppearanceItemCaption != null)
                    lciItemPrice.AppearanceItemCaption.BackColor = Color.Empty;
                lcItemID.Text = @"<href=" + D.GetAffiliateLink() + @">" + D.ItemID + @"</href>";
                lcTerm.Text = D.Term;
                lcTitle.Text = D.Title;
                lcCondition.Text = D.Condition;
                lcReturns.Text = D.Returns;
                lcBestOffer.Text = D.BestOffer.ToString();
                lcFoundTime.Text = D.FoundTime.ToString();
                lcLocation.Text = D.Location;
                lcFromCountry.Text = D.FromCountry;
                lcAutoPay.Text = D.CommitToBuy.ToString();
                lcCategoryID.Text = D.CategoryID;
                lcCategoryName.Text = D.CategoryName;
                lcConditionDescription.Text = D.ConditionDescription;
                lcFeedbackRating.Text = D.FeedbackRating.ToString(CultureInfo.InvariantCulture);
                lcFeedbackScore.Text = D.FeedbackScore.ToString(CultureInfo.InvariantCulture);
                lcPostedTime.Text = D.StartTimeLocal.ToString();
                lcSellerName.Text = D.SellerName;
                lcSoldTime.Text = D.SoldTime?.ToString();
                lcEbayWebsite.Text = D.Site.ToString();
                lcPageViews.Text = D.PageViews.ToString();
                lcUPC.Text = D.UPC;
                lcVariation.Text = D.Variation.ToString();
                lcBids.Text = D.Bids.ToString();
                lcListingType.Text = D.ListingTypeToString();
                lcAuctionPrice.Text = D.ItemPricing.AuctionPrice.FormatPrice();
                lcToCountry.Text = D.ToCountry;
                lcShippingDays.Text = D.ShippingDays.ToString();

                if (lcToCountry.Text.Length > 50)
                    lcToCountry.Text = $@"{lcToCountry.Text.Substring(0, 50)}...";

                lcPayment.Text = string.Join(", ", D.Payment);
                if (lcPayment.Text.Length > 50)
                    lcPayment.Text = lcPayment.Text.Substring(0, 50) + @"...";

                spinEditBuyNowQuantity.Properties.MaxValue = 1;
                spinEditBestOfferItemQuantity.Properties.MaxValue = 1;

                spinEditBuyNowQuantity.Properties.Buttons[1].Appearance.Options.UseForeColor = Placeoffer.PurchaseAllQuantity;
                spinEditBuyNowQuantity.Properties.Buttons[1].Appearance.Options.UseTextOptions = Placeoffer.PurchaseAllQuantity;
                chkSubstractShipping.Checked = Placeoffer.BestOfferSubtractShipping;

                AddCategorySpecificsControls();
                ItemSpecifics.SetSpecificForLayoutControlValue(layoutControl1, Row);

                AssignEbayAccountDropdown();

                if (D.BestOffer)
                    lciMakeOffertab.Visibility = LayoutVisibility.Always;
                else
                {
                    lciMakeOffertab.ParentTabbedGroup.SelectedTabPageIndex = 0;
                    lciMakeOffertab.Visibility = LayoutVisibility.OnlyInCustomization;
                }

                if (!D.CommitToBuy && !D.Variation)
                {
                    btnBuyItnow.Enabled = false;
                    lcQuantity.Enabled = false;
                }
                else
                {
                    if (!Program.Sandbox)
                    {
                        btnBuyItnow.Enabled = true;
                    }

                    btnBuyItnow.Text = @"Buy it Now";
                    btnBuyItnow.ImageOptions.SvgImage = null;
                    lcQuantity.Enabled = true;
                }

                AssignEbayLogoEtc();

                SetOfferMessage();
                UpdateQuantity(D);
                if (Placeoffer.PurchaseAllQuantity)
                    spinEditBuyNowQuantity.EditValue = D.QuantityAvailable;
                else
                    spinEditBuyNowQuantity.EditValue = 1;
                layoutControl1.EndUpdate();

                SetPricingAndShipping(D);
                SetButtonFocus(D.Order.OrderAction);
                await AssignThumbnail();

                if (CreditCardService.CreditCardPaymentEnabled)
                {
                    if (!D.CommitToBuy && !D.Variation)
                    {
                        btnBuyItnow.Text = "Checkout";
                        btnBuyItnow.Enabled = true;
                        lcQuantity.Enabled = true;
                        btnBuyItnow.ImageOptions.SvgImage = Resources.CreditCard;
                        btnBuyItnow.ImageOptions.SvgImageSize = new Size(16, 16);
                    }
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("SetupFormInfo: ", ex);
            }
        }

        private async Task AssignThumbnail()
        {
            try
            {
                pictureEdit1.BackColor = BackColor;
                var imageFilePath = await ImageTools.GetImageFromDiskOrInternet(D.GalleryUrl);
                if (File.Exists(imageFilePath))
                    pictureEdit1.Image = await ImageTools.ReadFileToBitmap(imageFilePath);

                if (pictureEdit1.Image == null)
                    pictureEdit1.Properties.NullText = "-";
            }
            catch
            {
                // ignored
            }
        }

        private void AssignEbayLogoEtc()
        {
            if (Program.Sandbox)
            {
                rdioShippingOptions.Properties.Items.Clear();
                lcEbayAccount.Properties.Items?.Clear();
                lcEbayAccount.Properties.Items?.AddRange(Form1.EBayAccountsList.Select(a => a.UserName).ToList());
                foreach (var ebayAccount in Form1.EBayAccountsList)
                {
                    lcEbayAccount.SelectedItem = ebayAccount.UserName;
                }

                lcQuantity.Enabled = true;
                lcTax.Visibility = LayoutVisibility.Always;
                lciShippingOptionsTab.Visibility = LayoutVisibility.Always;
            }
            else
            {
                pictureEditLogo.Visible = false;
                linkeBayPrivacyPolicy.Visible = false;
                linkeBayPrivacyPolicy.Visible = false;
                lciShippingOptionsTab.Visibility = LayoutVisibility.Never;
            }
        }

        private void AssignEbayAccountDropdown()
        {
            lcEbayAccount.Properties.Items?.Clear();
            lcEbayAccount.EditValue = null;
            lcEbayAccount.Properties.Items?.AddRange(Form1.EBayAccountsList.Select(a => a.UserName).ToList());

            foreach (var ebayAccount in Form1.EBayAccountsList)
            {
                if (D.EbayAccount?.UserName == ebayAccount.UserName)
                {
                    lcEbayAccount.SelectedItem = ebayAccount.UserName;
                }
            }
        }

        private void SetPricingAndShipping(DataList datalist)
        {
            lcItemPrice.Text = datalist.ItemPricing.ItemPrice.FormatPrice();
            lcShipAdditionalItem.Text = datalist.ItemShipping.ShipAdditionalItem.FormatPrice();

            var currencySign = Helpers.CurrencyCodeToSign(datalist.ItemPricing.GetTotalPrice(datalist.ItemShipping.FullSingleShippingPrice).Currency);
            currencySign = !string.IsNullOrWhiteSpace(currencySign) ? currencySign : $"({datalist.ItemPricing.GetTotalPrice(datalist.ItemShipping.FullSingleShippingPrice).Currency})";
            lciBestOfferPricePerItem.Text = "Offer Price Per Item ";
            spinEditBestOfferPricePerItem.Properties.Mask.EditMask = $@"{currencySign} \d+(\.\d{{1,2}})?";

            UpdateShippingAndTotal((decimal)spinEditBuyNowQuantity.EditValue);
        }

        private async void UpdateShippingAndTotal(decimal purchaseQuantity)
        {
            if (D.ItemShipping.ShippingStatusBrowseAPI != ItemShipping.ParsingStatus.Success && D.ItemShipping.ShippingStatusTradingAPI != ItemShipping.ParsingStatus.Success)
            {
                lcShipping.Text = "Unable to get shipping info";
                lcTotalPrice.Text = "-";
                return;
            }

            CurrencyAmount quantityShipPrice;
            if (D.ShippingType == "Calculated" || D.ShippingType == "CALCULATED")
            {
                var apiService = new ApiService(ConnectionConfig.GetGuestApiContext(D.EBaySite.SiteCode));
                Enum.TryParse(D.AvailableTo, true, out CountryCodeType destinationCountry);
                var shippingDetails = await apiService.GetItemShipping(D.ItemID, (int)purchaseQuantity, destinationCountry, D.Zip);
                var isInternational = D.AvailableTo != D.FromCountry;
                PricingService.TryParseShipping(shippingDetails, D.ItemShipping, isInternational);
                quantityShipPrice = D.ItemShipping.BareSingleShippingPrice;
            }
            else
            {
                quantityShipPrice = D.ItemShipping.GetQuantityShipPrice(int.Parse(purchaseQuantity.ToString(CultureInfo.InvariantCulture), CultureInfo.InvariantCulture), D.ShippingType);
            }

            var totalPrice = new CurrencyAmount(D.ItemPricing.ItemPrice.Value * (double)purchaseQuantity + quantityShipPrice.Value, D.ItemPricing.ItemPrice.Currency);

            lcShipping.Text = quantityShipPrice.FormatPrice();
            lcTotalPrice.Text = totalPrice.FormatPrice();
        }

        private void AddCategorySpecificsControls()
        {
            foreach (var specific in ItemSpecifics.CategorySpecificsList)
            {
                var columnName = specific.CategoryName;
                ItemSpecifics.AddSpecificToLayoutControl(columnName, layoutControl1);
            }
        }

        #endregion

        /// <summary>
        /// Determines if the current item is from a UK/GB private seller
        /// </summary>
        /// <returns>True if the seller is from UK/GB and is a private seller</returns>
        private bool IsUkGbPrivateSeller()
        {
            // Check if seller is from UK/GB
            var isUkGbSeller = IsSellerFromUkGb();

            // Check if seller is a private seller (not a business)
            var isPrivateSeller = !D.SellerIsBusiness;

            return isUkGbSeller && isPrivateSeller;
        }

        /// <summary>
        /// Checks if the seller is from UK/GB based on seller country or item location
        /// </summary>
        /// <returns>True if seller is from UK/GB</returns>
        private bool IsSellerFromUkGb()
        {
            // Check seller country first
            if (!string.IsNullOrEmpty(D.SellerCountry))
            {
                var sellerCountry = D.SellerCountry.ToUpperInvariant();
                if (sellerCountry == "UNITED KINGDOM" || sellerCountry == "UK" || sellerCountry == "GB")
                {
                    return true;
                }
            }

            // Fallback to item location country
            if (!string.IsNullOrEmpty(D.FromCountry))
            {
                var fromCountry = D.FromCountry.ToUpperInvariant();
                if (fromCountry == "UNITED KINGDOM" || fromCountry == "UK" || fromCountry == "GB")
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Updates the item price using the Trading API result (without fees)
        /// </summary>
        /// <param name="item">The item from Trading API</param>
        private void UpdatePriceFromTradingApi(ItemType item)
        {
            try
            {
                // Get the price from Trading API (this should be without fees)
                if (item.BuyItNowPrice != null)
                {
                    var tradingApiPrice = new CurrencyAmount(item.BuyItNowPrice.Value, item.BuyItNowPrice.currencyID.ToString());
                    D.ItemPricing.ItemPrice = tradingApiPrice;
                }
                else if (item.SellingStatus?.CurrentPrice != null)
                {
                    var tradingApiPrice = new CurrencyAmount(item.SellingStatus.CurrentPrice.Value, item.SellingStatus.CurrentPrice.currencyID.ToString());
                    D.ItemPricing.ItemPrice = tradingApiPrice;
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("UpdatePriceFromTradingApi: ", ex);
            }
        }

        private async void FormBid_Shown(object sender, EventArgs e)
        {
            _getItemCancelToken = new CancellationToken();

            var originalPrice = D.ItemPricing.ItemPrice.Value;

            // Check if this is a UK/GB private seller that requires additional GetItem call for accurate pricing
            var isUkGbPrivateSeller = IsUkGbPrivateSeller();

            try
            {
                // Always make the Trading API call for UK/GB private sellers to get price without fees
                // For other sellers, this call still provides updated item information
                await Task.Run(() => TradingAPIService.GetItem(ConnectionConfig.GetGuestApiContext(D.EBaySite.SiteCode), Row["ItemID"].ToString()), _getItemCancelToken).ContinueWith(a =>
                {
                    if (a.Result == null || _getItemCancelToken.IsCancellationRequested)
                        return;

                    ContinueFormSetup(a.Result, originalPrice, isUkGbPrivateSeller);
                }, TaskScheduler.FromCurrentSynchronizationContext());
            }
            catch (ApiException ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("FormBid Shown: ", ex);
            }

            if (spinEditBuyNowQuantity.EditValue != null)
            {
                if (Convert.ToInt32(spinEditBuyNowQuantity.EditValue) > 0)
                {
                    if (Placeoffer.PurchaseAllQuantity)
                    {
                        spinEditBuyNowQuantity.EditValue = D.QuantityAvailable;
                        spinEditBestOfferItemQuantity.EditValue = D.QuantityAvailable;
                    }
                    else
                    {
                        spinEditBestOfferItemQuantity.EditValue = 1;
                        spinEditBuyNowQuantity.EditValue = 1;
                    }
                }
            }
        }

        private void ContinueFormSetup(ItemType item, double originalPrice, bool isUkGbPrivateSeller = false)
        {
            DataList.ParseItem(D, item);

            // For UK/GB private sellers, update the price with the Trading API result (without fees)
            if (isUkGbPrivateSeller)
            {
                UpdatePriceFromTradingApi(item);
            }

            UpdateQuantity(D);
            if (item.SellingStatus.ListingStatus != ListingStatusCodeType.Active || item.ItemID == null)
            {
                XtraMessageBox.Show(this, En_US.Form1_ShowPlaceOffer_Listing_is_not_available, "", MessageBoxButtons.OK, MessageBoxIcon.Exclamation, MessageBoxDefaultButton.Button2);
            }
            else
            {
                if (D.QuantityAvailable > 0)
                {
                    UpdateQuantity(D);
                    if (Placeoffer.PurchaseAllQuantity)
                        spinEditBuyNowQuantity.EditValue = D.QuantityAvailable;
                    else
                        spinEditBuyNowQuantity.EditValue = 1;
                }

                lcPageViews.Text = item.HitCount.ToString();

                if (!originalPrice.Equals(D.ItemPricing.ItemPrice.Value))
                    ShowPriceChangedMessage();
            }
        }

        private void ShowPriceChangedMessage()
        {
            lcItemPrice.BackColor = Color.Salmon;
            lciItemPrice.AppearanceItemCaption.BackColor = Color.Salmon;
            var text = $"Seller has changed the price!\nNew item price = {D.ItemPricing.ItemPrice.Currency} {D.ItemPricing.ItemPrice.Value}";
            XtraMessageBox.Show(this, text, "Price", MessageBoxButtons.OK, MessageBoxIcon.Exclamation, MessageBoxDefaultButton.Button2);
        }

        private async void BtnPurchase_Click(object sender, EventArgs e)
        {
            try
            {
                var confirmationMessageBox = CreateConfirmationMessageBox();

                if (Program.Sandbox)
                {
                    if (await PurchaseOrderAPI(confirmationMessageBox))
                        return;
                }
                else
                {

                    var confirmationForm = new FormBidConfirmation();
                    var dialogResult = confirmationForm.ShowDialog(this);
                    if (dialogResult != DialogResult.Yes)
                    {
                        return;
                    }

                    if (CreditCardService.CreditCardPaymentEnabled && !D.CommitToBuy)
                    {
                        CreditCardCheckout.ExecuteCreditCardCheckout(D);
                        lcEbayAccount.Properties.Items?.Clear();
                        if (D.Order.UserName != null)
                        {
                            lcEbayAccount.Properties.Items?.Add(D.Order.UserName);
                            lcEbayAccount.SelectedItem = D.Order.UserName;
                        }
                    }
                    else
                    {
                        btnBuyItnow.Enabled = false;
                        BuyUsingPlaceOffer();
                        btnBuyItnow.Enabled = true;
                    }
                }

                _checkoutStopwatch.Stop();

                if (CreditCardService.CreditCardPaymentEnabled)
                    lcTitle.Text = Math.Round(_checkoutStopwatch.Elapsed.TotalSeconds, 1) + "s " + lcTitle.Text;

                var item = await ApiService.GetItemSafe(D.ItemID, ConnectionConfig.GetGuestApiContext(D.EBaySite.SiteCode));

                if (item == null)
                    return;

                DataList.ParseItem(D, item);
                UpdateQuantity(D);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("btnPurchase_Click: ", ex);
            }
        }

        private XtraMessageBoxForm CreateConfirmationMessageBox()
        {
            var top = (int)Math.Round((double)btnBuyItnow.Parent.Parent.Location.Y + btnBuyItnow.Location.Y
                //+ buttonBuyItNow.Height
                , 0);

            var msgBoxWidth = 254;
            var left = (int)Math.Round((double)(btnBuyItnow.Parent.Parent.Location.X + btnBuyItnow.Location.X), 0);
            left -= (msgBoxWidth - btnBuyItnow.Width) / 2;

            var confirmationMessageBox = new XtraMessageBoxForm { Top = top, Left = left };

            confirmationMessageBox.StartPosition = FormStartPosition.Manual;

            return confirmationMessageBox;
        }

        private void BuyUsingPlaceOffer()
        {
            var placeOfferCall = new PlaceOfferCall();
            try
            {
                if (!ValidateUserSelection() || !RetrieveEbayUser(out var ebayAccount))
                    return;
                var affiliateAction = D.Term == "Watchlist" ? "Watch" : "PlaceOffer";
                if (SetUpPlaceOffer(out placeOfferCall, affiliateAction, ebayAccount))
                    return;

                placeOfferCall.Offer.Quantity = (int)spinEditBuyNowQuantity.Value;
                placeOfferCall.Offer.Action = BidActionCodeType.Purchase;

                //if (Debugger.IsAttached)return;                
                placeOfferCall.Execute();

                //autopay
                var response = placeOfferCall.ApiResponse;
                if (!string.IsNullOrEmpty(response.OrderLineItemID))
                {
                    lblPurchaseResult.Text = DateTime.Now.ToString("HH:mm:ss") + @" Purchased " + response.OrderLineItemID;
                    lblPurchaseResult.ForeColor = Color.LimeGreen;
                    //AutoMeasurement.Client.TrackEvent("Bought_OK", "Purchasing", Analytics.GAid, 1);
                    Stat.PlaceOfferWonCounter++;
                    var placeOfferWonAmountUSD = CurrencyConverter.ConvertToUSD(placeOfferCall.Offer.MaxBid.Value * placeOfferCall.Offer.Quantity, placeOfferCall.Offer.MaxBid.currencyID.ToString());

                    Stat.PlaceOfferWonAmount += (int)placeOfferWonAmountUSD;
                    Pixel.Track(Pixel.EventType.PlaceOfferWin, placeOfferCall.ItemID, placeOfferWonAmountUSD);

                    return;
                }

                lblPurchaseResult.Text = DateTime.Now.ToString("HH:mm:ss") + @" Error in PlaceOffer. Empty response";
                lblPurchaseResult.ForeColor = Color.Red;
                //AutoMeasurement.Client.TrackEvent("Purchase_Empty", "Purchasing", Analytics.GAid, 1);
                Stat.PlaceOfferEmptyCounter++;

                if (!placeOfferCall.HasError)
                    return;

                if (response.Errors == null)
                    return;

                lblPurchaseResult.Text = TradingAPIService.ShowApiErrors(response.Errors);
                lblPurchaseResult.ForeColor = Color.OrangeRed;
            }
            catch (ApiException ex)
            {
                var errors = ex.Errors;
                if (placeOfferCall.HasError)
                {
                    lblPurchaseResult.Text = TradingAPIService.ShowApiErrors(errors);
                    lblPurchaseResult.ForeColor = Color.OrangeRed;
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("MakePlaceOfferCall: ", ex);
            }

            Pixel.Track(Pixel.EventType.PlaceOfferLost, placeOfferCall?.ItemID);
        }

        private bool SetUpPlaceOffer(out PlaceOfferCall placeOfferCall, string affiliateAction, EbayAccount ebayAccount)
        {
            placeOfferCall = new PlaceOfferCall();
            try
            {
                placeOfferCall.ApiContext = ConnectionConfig.GetApiContextPlaceOffer(D.EBaySite.SiteCode, ebayAccount.TokenPo);
                placeOfferCall.ApiRequest.InvocationID = Guid.NewGuid().ToString().Replace("-", "");
                placeOfferCall.AbstractRequest.EndUserIP = ProgramState.PublicIp;
                placeOfferCall.ItemID = D.ItemID;
                placeOfferCall.Offer = new OfferType();

                placeOfferCall.Offer.MaxBid = new AmountType()
                {
                    Value = D.ItemPricing.ItemPrice.Value,
                    currencyID = (CurrencyCodeType)Enum.Parse(typeof(CurrencyCodeType), D.ItemPricing.ItemPrice.Currency)
                };
                
                if (!Program.Sandbox)
                    placeOfferCall.AffiliateTrackingDetails = new AffiliateTrackingDetailsType
                    {
                        AffiliateUserID = AffiliateTool.GetCustomIDLicenseLong(D.ItemID, affiliateAction),
                        ApplicationDeviceType = ApplicationDeviceTypeCodeType.Desktop,
                        TrackingID = Config.CampaignID,
                        TrackingPartnerCode = "9"
                    };

                return false;
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("SetUpPlaceOffer: ", ex);
            }

            return true;
        }

        private void MakeOfferUsingAPI(int offerQuantity, double offerTotalAmount, string affiliateAction)
        {
            var placeOfferCall = new PlaceOfferCall();
            try

            {
                if (!ValidateUserSelection() || !RetrieveEbayUser(out var ebayAccount))
                    return;

                if (SetUpPlaceOffer(out placeOfferCall, affiliateAction, ebayAccount))
                    return;

                placeOfferCall.Offer.Quantity = offerQuantity;
                placeOfferCall.Offer.MaxBid.Value = offerTotalAmount;
                placeOfferCall.Offer.Action = BidActionCodeType.Offer;
                placeOfferCall.Offer.Message = memoEditBestOfferMessage.Text;

                placeOfferCall.Execute();


                //autopay
                var response = placeOfferCall.ApiResponse;
                if (!string.IsNullOrEmpty(response.BestOffer?.BestOfferID))
                {
                    lblPurchaseResult.Text = DateTime.Now.ToString("HH:mm:ss") + $" Offer {response.BestOffer?.Status}";

                    if (response.BestOffer?.Status != BestOfferStatusCodeType.Accepted)
                    {
                        lblPurchaseResult.ForeColor = Color.Sienna;
                    }
                    else
                    {
                        lblPurchaseResult.ForeColor = Color.LimeGreen;
                    }

                    //AutoMeasurement.Client.TrackEvent("Offer_OK", "Purchasing", Analytics.GAid, 1);
                    Stat.MakeOfferOKCounter++;
                    Pixel.Track(Pixel.EventType.MakeOffer, placeOfferCall.ItemID);
                }
                else
                {
                    lblPurchaseResult.ForeColor = Color.Red;
                    lblPurchaseResult.Text = DateTime.Now.ToString("HH:mm:ss") + @" Offer request failed";
                    Stat.MakeOfferEmptyCounter++;
                    //AutoMeasurement.Client.TrackEvent("Offer_Empty", "Purchasing", Analytics.GAid, 1);
                }

                if (placeOfferCall.HasError)
                {
                    var errors = response.Errors;
                    if (errors != null)
                    {
                        lblPurchaseResult.Text = TradingAPIService.ShowApiErrors(errors);
                        lblPurchaseResult.ForeColor = Color.OrangeRed;
                    }
                }
            }
            catch (ApiException ex)
            {
                var errors = ex.Errors;
                if (placeOfferCall.HasError)
                {
                    lblPurchaseResult.Text = TradingAPIService.ShowApiErrors(errors);
                    lblPurchaseResult.ForeColor = Color.OrangeRed;
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Make Offer: ", ex);
            }
        }

        private bool ValidateUserSelection()
        {
            if (lcEbayAccount == null || string.IsNullOrEmpty(lcEbayAccount.SelectedItem.ToString()))
            {
                XtraMessageBox.Show("No user selected");
                return false;
            }

            return true;
        }

        private bool RetrieveEbayUser(out EbayAccount eBayUser)
        {
            eBayUser = Form1.EBayAccountsList.FirstOrDefault(a => a.UserName == lcEbayAccount.SelectedItem.ToString());
            if (eBayUser == null)
            {
                XtraMessageBox.Show($"User '{lcEbayAccount.SelectedItem}' not found");
                return false;
            }

            return true;
        }

        private void UpdateQuantity(DataList datalist)
        {
            try
            {
                var quantityAvailable = datalist.QuantityAvailable;
                if (quantityAvailable < 1)
                {
                    spinEditBuyNowQuantity.EditValue = 0;
                    spinEditBestOfferItemQuantity.EditValue = 0;
                }

                lblAvailableCount.Text = quantityAvailable.ToString();
                lblSold.Text = (datalist.QuantityTotal - datalist.QuantityAvailable).ToString();
                spinEditBuyNowQuantity.Properties.MaxValue = quantityAvailable;
                spinEditBestOfferItemQuantity.Properties.MaxValue = quantityAvailable;
                UpdateOfferTotalPrice();
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Update Quantity: ", ex);
            }
        }

        private void BuyNowQuantityChanged(object sender, EventArgs e)
        {
            var spinEditQuantity = (SpinEdit)sender;
            if (spinEditQuantity.Value == 0 || ((SpinEdit)sender).Properties.MaxValue == 0)
            {
                layoutControl1.BeginUpdate();
                btnBuyItnow.Enabled = false;
                lcTotalPrice.Text = "0";
                layoutControl1.EndUpdate();
                if (Debugger.IsAttached)
                    btnBuyItnow.Enabled = true;
            }
            else
            {
                layoutControl1.BeginUpdate();
                if (Row.RowState != DataRowState.Detached)
                    if (D.CommitToBuy || D.Variation)
                    {
                        btnBuyItnow.Enabled = true;
                    }

                layoutControl1.EndUpdate();
                UpdateShippingAndTotal(spinEditQuantity.Value);
            }

            UpdateQuantity(D);
        }

        private void spinEditBuyNowQuantity_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            if (e.Button.Tag.ToString() == "AllQuantity")
            {
                e.Button.Appearance.Options.UseForeColor = !e.Button.Appearance.Options.UseForeColor;
                e.Button.Appearance.Options.UseTextOptions = !e.Button.Appearance.Options.UseForeColor;
                Placeoffer.PurchaseAllQuantity = !Placeoffer.PurchaseAllQuantity;
                spinEditBuyNowQuantity.EditValue = D.QuantityAvailable;
            }
        }

        private void lcEbayAccount_SelectedValueChanged(object sender, EventArgs e)
        {
            D.Order.UserName = ((ComboBoxEdit)sender).EditValue.ToString();
        }

        private void lblPurchaseResult_HyperlinkClick(object sender, DevExpress.Utils.HyperlinkClickEventArgs e)
        {
            Process.Start(e.Link);
        }
    }
}
