﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="ably.io" version="1.2.11" targetFramework="net462" />
  <package id="BouncyCastle" version="1.8.9" targetFramework="net462" />
  <package id="CefSharp.Common" version="131.3.10" targetFramework="net462" />
  <package id="CefSharp.WinForms" version="131.3.10" targetFramework="net462" />
  <package id="chromiumembeddedframework.runtime.win-x64" version="131.3.1" targetFramework="net462" />
  <package id="chromiumembeddedframework.runtime.win-x86" version="131.3.1" targetFramework="net462" />
  <package id="DalSoft.RestClient" version="4.4.1" targetFramework="net462" />
  <package id="ini-parser" version="2.5.2" targetFramework="net462" />
  <package id="J2N" version="2.0.0-beta-0017" targetFramework="net462" />
  <package id="LicenseSpot.Framework" version="3.9.0" targetFramework="net462" />
  <package id="Lucene.Net" version="4.8.0-beta00015" targetFramework="net462" />
  <package id="Lucene.Net.Analysis.Common" version="4.8.0-beta00015" targetFramework="net462" />
  <package id="Lucene.Net.Memory" version="4.8.0-beta00015" targetFramework="net462" />
  <package id="Lucene.Net.Queries" version="4.8.0-beta00015" targetFramework="net462" />
  <package id="Lucene.Net.QueryParser" version="4.8.0-beta00015" targetFramework="net462" />
  <package id="Lucene.Net.Sandbox" version="4.8.0-beta00015" targetFramework="net462" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.2" targetFramework="net462" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net462" />
  <package id="Microsoft.Extensions.Configuration" version="9.0.2" targetFramework="net462" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="9.0.2" targetFramework="net462" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="9.0.2" targetFramework="net462" />
  <package id="Microsoft.Extensions.DependencyInjection" version="9.0.2" targetFramework="net462" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="9.0.2" targetFramework="net462" />
  <package id="Microsoft.Extensions.Http" version="9.0.2" targetFramework="net462" />
  <package id="Microsoft.Extensions.Logging" version="9.0.2" targetFramework="net462" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="9.0.2" targetFramework="net462" />
  <package id="Microsoft.Extensions.Options" version="9.0.2" targetFramework="net462" />
  <package id="Microsoft.Extensions.Primitives" version="9.0.2" targetFramework="net462" />
  <package id="Microsoft.Net.Compilers" version="4.2.0" targetFramework="net462" developmentDependency="true" />
  <package id="Microsoft.NETCore.Platforms" version="6.0.4" targetFramework="net462" />
  <package id="Microsoft.Win32.Primitives" version="4.3.0" targetFramework="net462" />
  <package id="Microsoft.WindowsAzure.ConfigurationManager" version="3.2.3" targetFramework="net46" />
  <package id="MQTTnet" version="4.3.3.952" targetFramework="net462" />
  <package id="MQTTnet.Extensions.ManagedClient" version="4.3.3.952" targetFramework="net462" />
  <package id="MQTTnet.Extensions.WebSocket4Net" version="4.3.3.952" targetFramework="net462" />
  <package id="MsgPack.Cli" version="1.0.1" targetFramework="net46" />
  <package id="NETStandard.Library" version="2.0.3" targetFramework="net462" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net462" />
  <package id="NLog" version="5.0.4" targetFramework="net462" />
  <package id="Stub.System.Data.SQLite.Core.NetFramework" version="1.0.119.0" targetFramework="net462" />
  <package id="SumoLogic.Logging.Common" version="1.0.1.5" targetFramework="net462" />
  <package id="SumoLogic.Logging.NLog" version="1.0.1.5" targetFramework="net462" />
  <package id="SuperSocket.ClientEngine.Core" version="0.10.0" targetFramework="net462" />
  <package id="System.AppContext" version="4.3.0" targetFramework="net462" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net462" />
  <package id="System.Collections" version="4.3.0" targetFramework="net462" />
  <package id="System.Collections.Concurrent" version="4.3.0" targetFramework="net462" />
  <package id="System.Collections.Specialized" version="4.3.0" targetFramework="net462" />
  <package id="System.ComponentModel.Annotations" version="5.0.0" targetFramework="net462" />
  <package id="System.Console" version="4.3.1" targetFramework="net462" />
  <package id="System.Data.SQLite.Core" version="1.0.119.0" targetFramework="net462" />
  <package id="System.Diagnostics.Debug" version="4.3.0" targetFramework="net462" />
  <package id="System.Diagnostics.DiagnosticSource" version="9.0.2" targetFramework="net462" />
  <package id="System.Diagnostics.Tools" version="4.3.0" targetFramework="net462" />
  <package id="System.Diagnostics.Tracing" version="4.3.0" targetFramework="net462" />
  <package id="System.Globalization" version="4.3.0" targetFramework="net462" />
  <package id="System.Globalization.Calendars" version="4.3.0" targetFramework="net462" />
  <package id="System.IO" version="4.3.0" targetFramework="net462" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net462" />
  <package id="System.IO.Compression.ZipFile" version="4.3.0" targetFramework="net462" />
  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="net462" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net462" />
  <package id="System.Linq" version="4.3.0" targetFramework="net462" />
  <package id="System.Linq.Expressions" version="4.3.0" targetFramework="net462" />
  <package id="System.Memory" version="4.5.5" targetFramework="net462" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net462" />
  <package id="System.Net.Http.WinHttpHandler" version="6.0.1" targetFramework="net462" />
  <package id="System.Net.NameResolution" version="4.3.0" targetFramework="net462" />
  <package id="System.Net.Primitives" version="4.3.1" targetFramework="net462" />
  <package id="System.Net.Requests" version="4.3.0" targetFramework="net462" />
  <package id="System.Net.Security" version="4.3.0" targetFramework="net462" />
  <package id="System.Net.Sockets" version="4.3.0" targetFramework="net462" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net462" />
  <package id="System.ObjectModel" version="4.3.0" targetFramework="net462" />
  <package id="System.Reflection" version="4.3.0" targetFramework="net462" />
  <package id="System.Reflection.Extensions" version="4.3.0" targetFramework="net462" />
  <package id="System.Reflection.Primitives" version="4.3.0" targetFramework="net462" />
  <package id="System.Resources.ResourceManager" version="4.3.0" targetFramework="net462" />
  <package id="System.Runtime" version="4.3.1" targetFramework="net462" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net462" />
  <package id="System.Runtime.Extensions" version="4.3.1" targetFramework="net462" />
  <package id="System.Runtime.Handles" version="4.3.0" targetFramework="net462" />
  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="net462" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net462" />
  <package id="System.Runtime.Numerics" version="4.3.0" targetFramework="net462" />
  <package id="System.Runtime.WindowsRuntime" version="5.0.0-preview.5.20278.1" targetFramework="net462" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.1" targetFramework="net46" requireReinstallation="true" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net46" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net46" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.2" targetFramework="net462" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net462" />
  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="net462" />
  <package id="System.Text.RegularExpressions" version="4.3.1" targetFramework="net462" />
  <package id="System.Threading" version="4.3.0" targetFramework="net462" />
  <package id="System.Threading.Channels" version="7.0.0" targetFramework="net462" />
  <package id="System.Threading.Tasks" version="4.3.0" targetFramework="net462" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net462" />
  <package id="System.Threading.Timer" version="4.3.0" targetFramework="net462" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net462" />
  <package id="System.Xml.ReaderWriter" version="4.3.1" targetFramework="net462" />
  <package id="System.Xml.XDocument" version="4.3.0" targetFramework="net462" />
  <package id="Telegram.Bot" version="18.0.0" targetFramework="net462" />
  <package id="Telegram.Bot.Extensions.Polling" version="1.0.2" targetFramework="net462" />
  <package id="WebSocket4Net" version="0.15.2" targetFramework="net462" />
</packages>