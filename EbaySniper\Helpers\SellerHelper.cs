using System;
using System.Globalization;
using System.Linq;
using eBay.Service.Core.Soap;
using uBuyFirst.API.ShoppingAPI;
using uBuyFirst.Data;
using uBuyFirst.Models;
using uBuyFirst.Prefs;
using uBuyFirst.Services;

namespace uBuyFirst.Helpers
{
    /// <summary>
    /// Utility class for seller-related operations including validation, filtering, and business logic
    /// </summary>
    public static class SellerHelper
    {
        #region Seller Validation & Filtering

        /// <summary>
        /// Validates if a seller is allowed based on keyword filters and blocked seller list
        /// </summary>
        /// <param name="sellerName">The seller name to validate</param>
        /// <param name="keyword2Find">The keyword configuration containing seller filters</param>
        /// <returns>Validation result with success status and error message</returns>
        public static SellerValidationResult ValidateSeller(string sellerName, Keyword2Find keyword2Find)
        {
            // Check keyword seller filters
            if (keyword2Find is { SellerType: "Include", Sellers.Length: > 0 })
            {
                if (!keyword2Find.Sellers.Contains(sellerName))
                {
                    return new SellerValidationResult(false, "Seller doesn't match");
                }
            }

            // Check blocked sellers list
            if (UserSettings.BlockedSellers.Contains(sellerName))
            {
                return new SellerValidationResult(false, "Seller in block list");
            }

            return new SellerValidationResult(true, null);
        }

        /// <summary>
        /// Checks if a seller is in the blocked sellers list
        /// </summary>
        /// <param name="sellerName">The seller name to check</param>
        /// <returns>True if seller is blocked</returns>
        public static bool IsSellerBlocked(string sellerName)
        {
            return UserSettings.BlockedSellers.Contains(sellerName);
        }

        #endregion

        #region Seller Country & Location Logic

        /// <summary>
        /// Determines if the seller is from UK/GB based on seller country or item location
        /// </summary>
        /// <param name="datalist">The DataList containing location information</param>
        /// <returns>True if seller is from UK/GB</returns>
        public static bool IsSellerFromUkGb(DataList datalist)
        {
            // Check seller country first
            if (!string.IsNullOrEmpty(datalist.SellerCountry))
            {
                var sellerCountry = datalist.SellerCountry.ToUpperInvariant();
                if (IsUkGbCountry(sellerCountry))
                {
                    return true;
                }
            }

            // Fallback to item location country
            if (!string.IsNullOrEmpty(datalist.FromCountry))
            {
                var fromCountry = datalist.FromCountry.ToUpperInvariant();
                if (IsUkGbCountry(fromCountry))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Checks if a country string represents UK/GB
        /// </summary>
        /// <param name="country">Country string to check (case-insensitive)</param>
        /// <returns>True if country represents UK/GB</returns>
        public static bool IsUkGbCountry(string country)
        {
            if (string.IsNullOrEmpty(country))
                return false;

            var upperCountry = country.ToUpperInvariant();
            return upperCountry == "UNITED KINGDOM" || upperCountry == "UK" || upperCountry == "GB";
        }

        /// <summary>
        /// Gets the seller country from SellerUser object using CountryService
        /// </summary>
        /// <param name="sellerUser">The SellerUser object</param>
        /// <param name="countryService">The CountryService instance</param>
        /// <returns>Seller country name</returns>
        public static string GetSellerCountry(SellerUser sellerUser, CountryService countryService)
        {
            return countryService.GetSellerCountry(sellerUser);
        }

        #endregion

        #region Seller Business Type Logic

        /// <summary>
        /// Determines if the seller is a UK/GB private seller (requires special pricing handling)
        /// </summary>
        /// <param name="datalist">The DataList containing seller information</param>
        /// <returns>True if the seller is from UK/GB and is a private seller</returns>
        public static bool IsUkGbPrivateSeller(DataList datalist)
        {
            // Check if seller is from UK/GB
            var isUkGbSeller = IsSellerFromUkGb(datalist);

            // Check if seller is a private seller (not a business)
            var isPrivateSeller = !datalist.SellerIsBusiness;

            return isUkGbSeller && isPrivateSeller;
        }

        /// <summary>
        /// Checks if the seller is a business seller
        /// </summary>
        /// <param name="datalist">The DataList containing seller information</param>
        /// <returns>True if seller is a business</returns>
        public static bool IsBusinessSeller(DataList datalist)
        {
            return datalist.SellerIsBusiness;
        }

        /// <summary>
        /// Checks if the seller is a private seller
        /// </summary>
        /// <param name="datalist">The DataList containing seller information</param>
        /// <returns>True if seller is private (not business)</returns>
        public static bool IsPrivateSeller(DataList datalist)
        {
            return !datalist.SellerIsBusiness;
        }

        #endregion

        #region Seller Store Information

        /// <summary>
        /// Extracts store name from ItemType (Trading API)
        /// </summary>
        /// <param name="item">The ItemType object</param>
        /// <returns>Store name or empty string</returns>
        public static string GetStoreName(ItemType item)
        {
            if (!item.Seller.SellerInfo.StoreOwner || item.Storefront == null)
                return "";

            if (item.Storefront.StoreName != null)
                return item.Storefront.StoreName;

            if (item.Storefront.StoreURL != null)
                return System.Text.RegularExpressions.Regex.Replace(item.Storefront.StoreURL, ".*/", "");

            if (item.Seller.SellerInfo.StoreOwner)
                return "Store";

            return "Store";
        }

        /// <summary>
        /// Extracts store name from SimpleItemType (Shopping API)
        /// </summary>
        /// <param name="item">The SimpleItemType object</param>
        /// <returns>Store name or empty string</returns>
        public static string GetStoreName(ShoppingAPIJson.SimpleItemType item)
        {
            if (item.Storefront == null)
                return "";

            if (item.Storefront.StoreName != null)
                return item.Storefront.StoreName;

            if (item.Storefront.StoreURL != null)
                return System.Text.RegularExpressions.Regex.Replace(item.Storefront.StoreURL, ".*/", "");

            return "Store";
        }

        #endregion

        #region Seller Data Population

        /// <summary>
        /// Populates seller information in DataList from SellerUser object
        /// </summary>
        /// <param name="datalist">The DataList to populate</param>
        /// <param name="sellerUser">The SellerUser containing seller information</param>
        /// <param name="countryService">The CountryService for country mapping</param>
        public static void PopulateSellerInfo(DataList datalist, SellerUser sellerUser, CountryService countryService)
        {
            if (sellerUser == null || string.IsNullOrEmpty(sellerUser.UserName))
                return;

            datalist.SellerCountry = GetSellerCountry(sellerUser, countryService);

            try
            {
                datalist.SellerRegistration = DateTime.ParseExact(sellerUser.RegDate, "yyyyMMdd", CultureInfo.InvariantCulture);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            datalist.SellerStore = sellerUser.StoreName ?? "";
            datalist.SellerIsBusiness = sellerUser.Business;
        }

        /// <summary>
        /// Populates basic seller information from Trading API ItemType
        /// </summary>
        /// <param name="datalist">The DataList to populate</param>
        /// <param name="item">The ItemType containing seller information</param>
        public static void PopulateSellerInfoFromItem(DataList datalist, ItemType item)
        {
            datalist.SellerName = item.Seller?.UserID ?? "";
            datalist.FeedbackRating = (decimal)(item.Seller?.PositiveFeedbackPercent ?? 0.0);
            datalist.FeedbackScore = item.Seller?.FeedbackScore ?? 0;
            datalist.StoreName = GetStoreName(item);
        }

        /// <summary>
        /// Populates basic seller information from Shopping API SimpleItemType
        /// </summary>
        /// <param name="datalist">The DataList to populate</param>
        /// <param name="item">The SimpleItemType containing seller information</param>
        public static void PopulateSellerInfoFromSimpleItem(DataList datalist, ShoppingAPIJson.SimpleItemType item)
        {
            datalist.SellerName = item.Seller?.UserID ?? "";
            datalist.FeedbackRating = (decimal)(item.Seller?.PositiveFeedbackPercent ?? 0);
            datalist.FeedbackScore = item.Seller?.FeedbackScore ?? 0;
            datalist.StoreName = GetStoreName(item);
        }

        #endregion
    }

    /// <summary>
    /// Result of seller validation operation
    /// </summary>
    public class SellerValidationResult
    {
        public bool IsValid { get; }
        public string ErrorMessage { get; }

        public SellerValidationResult(bool isValid, string errorMessage)
        {
            IsValid = isValid;
            ErrorMessage = errorMessage;
        }
    }
}
